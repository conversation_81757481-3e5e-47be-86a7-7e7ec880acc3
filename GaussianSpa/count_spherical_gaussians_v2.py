#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
读取spherical_gaussian_model_cullSG_v2.py中输出的checkpoint或.ply文件，获取对应的高斯数量和球面高斯基轴数统计
支持variable_sg_bands参数的列表格式和统一格式
"""

import os
import sys
import torch
import argparse
import numpy as np
from collections import Counter
from plyfile import PlyData


def analyze_sharpness_distribution(sg_sharpness, verbose=False):
    """
    分析球面高斯基锐度（Sharpness）分布
    """
    print("\n=== 球面高斯基锐度（Sharpness）分布 ===")

    # 收集所有锐度值
    all_sharpness_values = []

    if isinstance(sg_sharpness, list):
        # Variable SG Bands格式：列表中的每个元素对应一个度数
        for degree, tensor in enumerate(sg_sharpness):
            if tensor is not None and tensor.numel() > 0:
                # 取绝对值并展平
                sharpness_flat = torch.abs(tensor.flatten())
                all_sharpness_values.append(sharpness_flat)
                if verbose:
                    print(f"度数 {degree}: {tensor.numel()} 个锐度值")
    else:
        # 统一格式：单个张量
        if sg_sharpness is not None and sg_sharpness.numel() > 0:
            sharpness_flat = torch.abs(sg_sharpness.flatten())
            all_sharpness_values.append(sharpness_flat)
            if verbose:
                print(f"统一格式: {sg_sharpness.numel()} 个锐度值")

    if not all_sharpness_values:
        print("没有找到有效的锐度数据，无法打印分布。")
        return

    # 合并所有锐度值
    all_sharpness_values = torch.cat(all_sharpness_values)

    if all_sharpness_values.numel() == 0:
        print("锐度值为空，无法打印分布。")
        return

    # 转换为numpy进行分析
    sharpness_np = all_sharpness_values.cpu().numpy()

    print(f"总锐度值数量: {len(sharpness_np)}")
    print(f"最小值: {np.min(sharpness_np):.6f}")
    print(f"最大值: {np.max(sharpness_np):.6f}")
    print(f"平均值: {np.mean(sharpness_np):.6f}")
    print(f"中位数: {np.median(sharpness_np):.6f}")

    # 定义固定的区间边界
    bin_edges = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, float('inf')]
    bin_labels = [
        "[0.0, 0.2)",
        "[0.2, 0.4)",
        "[0.4, 0.6)",
        "[0.6, 0.8)",
        "[0.8, 1.0)",
        "[1.0, 1.2)",
        "[1.2, 1.4)",
        "[1.4, 1.6)",
        "[1.6, 1.8)",
        "[1.8, +∞)"
    ]

    # 计算每个区间的计数
    hist_counts = []
    for i in range(len(bin_edges) - 1):
        if i == len(bin_edges) - 2:  # 最后一个区间 (>1.8)
            mask = sharpness_np >= bin_edges[i]
        else:
            mask = (sharpness_np >= bin_edges[i]) & (sharpness_np < bin_edges[i+1])
        count = np.sum(mask)
        hist_counts.append(count)

    max_count = max(hist_counts) if hist_counts else 0
    bar_char = '█'
    max_bar_length = 50  # 终端中柱状图的最大长度

    print("\n--- 锐度分布柱状图 ---")
    for i, (label, count) in enumerate(zip(bin_labels, hist_counts)):
        if max_count > 0:
            bar_length = int((count / max_count) * max_bar_length)
        else:
            bar_length = 0
        percentage = (count / len(sharpness_np)) * 100

        print(f"{label:<12} ({count: <5d}, {percentage:5.2f}%): {bar_char * bar_length}")
    print("-----------------------\n")


def analyze_sharpness_distribution_from_ply(ply_elements, verbose=False):
    """
    从PLY文件元素中分析球面高斯基锐度（Sharpness）分布
    """
    print("\n=== 球面高斯基锐度（Sharpness）分布 ===")

    all_sharpness_values = []

    # 收集所有锐度值
    for element in ply_elements:
        # 查找锐度相关的属性
        sharpness_attrs = [name for name in element.data.dtype.names if 'sharpness' in name.lower()]

        if not sharpness_attrs:
            if verbose:
                print(f"元素 {element.name}: 未找到锐度属性")
            continue

        for attr in sharpness_attrs:
            sharpness_data = np.asarray(element.data[attr])
            # 取绝对值
            sharpness_data = np.abs(sharpness_data)
            all_sharpness_values.extend(sharpness_data.flatten())

            if verbose:
                print(f"元素 {element.name}, 属性 {attr}: {len(sharpness_data)} 个锐度值")

    if not all_sharpness_values:
        print("没有找到有效的锐度数据，无法打印分布。")
        return

    all_sharpness_values = np.array(all_sharpness_values)

    if len(all_sharpness_values) == 0:
        print("锐度值为空，无法打印分布。")
        return

    print(f"总锐度值数量: {len(all_sharpness_values)}")
    print(f"最小值: {np.min(all_sharpness_values):.6f}")
    print(f"最大值: {np.max(all_sharpness_values):.6f}")
    print(f"平均值: {np.mean(all_sharpness_values):.6f}")
    print(f"中位数: {np.median(all_sharpness_values):.6f}")

    # 定义固定的区间边界
    bin_edges = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, float('inf')]
    bin_labels = [
        "[0.0, 0.2)",
        "[0.2, 0.4)",
        "[0.4, 0.6)",
        "[0.6, 0.8)",
        "[0.8, 1.0)",
        "[1.0, 1.2)",
        "[1.2, 1.4)",
        "[1.4, 1.6)",
        "[1.6, 1.8)",
        "[1.8, +∞)"
    ]

    # 计算每个区间的计数
    hist_counts = []
    for i in range(len(bin_edges) - 1):
        if i == len(bin_edges) - 2:  # 最后一个区间 (>1.8)
            mask = all_sharpness_values >= bin_edges[i]
        else:
            mask = (all_sharpness_values >= bin_edges[i]) & (all_sharpness_values < bin_edges[i+1])
        count = np.sum(mask)
        hist_counts.append(count)

    max_count = max(hist_counts) if hist_counts else 0
    bar_char = '█'
    max_bar_length = 50  # 终端中柱状图的最大长度

    print("\n--- 锐度分布柱状图 ---")
    for i, (label, count) in enumerate(zip(bin_labels, hist_counts)):
        if max_count > 0:
            bar_length = int((count / max_count) * max_bar_length)
        else:
            bar_length = 0
        percentage = (count / len(all_sharpness_values)) * 100

        print(f"{label:<12} ({count: <5d}, {percentage:5.2f}%): {bar_char * bar_length}")
    print("-----------------------\n")


def analyze_axis_statistics(axis_counts, verbose=False):
    """
    分析球面高斯基轴数统计
    """
    print("\n=== 球面高斯基轴数统计 ===")

    # 计算总轴数
    total_axes = np.sum(axis_counts)
    total_gaussians = len(axis_counts)
    print(f"球面高斯基总轴数: {total_axes}")
    print(f"平均每个高斯的轴数: {total_axes / total_gaussians:.2f}")

    # 统计不同轴数的高斯数量
    unique_counts, frequencies = np.unique(axis_counts, return_counts=True)

    print(f"\n轴数分布:")
    for axis_count, frequency in zip(unique_counts, frequencies):
        percentage = (frequency / len(axis_counts)) * 100
        # 创建简单的条形图
        bar_length = int(percentage / 2)  # 每2%一个字符
        bar = "█" * bar_length
        print(f"  {axis_count} 轴: {frequency:6d} 个高斯 ({percentage:5.2f}%) {bar}")

    # 计算统计信息
    mean_axes = np.mean(axis_counts)
    median_axes = np.median(axis_counts)
    std_axes = np.std(axis_counts)
    min_axes = np.min(axis_counts)
    max_axes = np.max(axis_counts)

    print(f"\n轴数统计信息:")
    print(f"  平均轴数: {mean_axes:.3f}")
    print(f"  中位数轴数: {median_axes:.3f}")
    print(f"  标准差: {std_axes:.3f}")
    print(f"  最小轴数: {min_axes}")
    print(f"  最大轴数: {max_axes}")

    # 计算剪枝效果（假设最大轴数是原始轴数）
    if max_axes > 0:
        original_total_axes = total_gaussians * max_axes
        pruning_ratio = (original_total_axes - total_axes) / original_total_axes * 100
        print(f"\n剪枝效果分析:")
        print(f"  原始总轴数（假设）: {original_total_axes}")
        print(f"  当前总轴数: {total_axes}")
        print(f"  剪枝比例: {pruning_ratio:.2f}%")

    if verbose:
        # 显示更详细的分布信息
        print(f"\n详细分布信息:")
        percentiles = [25, 50, 75, 90, 95, 99]
        for p in percentiles:
            value = np.percentile(axis_counts, p)
            print(f"  {p}% 分位数: {value:.2f}")

        # 显示零轴高斯的数量
        zero_axis_count = np.sum(axis_counts == 0)
        if zero_axis_count > 0:
            print(f"\n零轴高斯数量: {zero_axis_count} ({zero_axis_count/total_gaussians*100:.2f}%)")


def analyze_variable_sg_bands_data(sg_directions, sg_sharpness, sg_rgb, verbose=False):
    """
    分析variable_sg_bands=True时的列表格式数据
    """
    print("\n=== Variable SG Bands 数据分析 ===")

    if not isinstance(sg_directions, list):
        print("错误: sg_directions不是列表格式")
        return None

    total_gaussians = 0
    axis_counts = []

    print(f"球面高斯基度数分布:")
    for sg_degree in range(len(sg_directions)):
        if sg_directions[sg_degree].numel() > 0:
            num_gaussians = sg_directions[sg_degree].shape[0]
            total_gaussians += num_gaussians
            # 每个gaussian在这个度数下有sg_degree个轴
            axis_counts.extend([sg_degree] * num_gaussians)
            print(f"  度数 {sg_degree}: {num_gaussians} 个高斯")
        else:
            print(f"  度数 {sg_degree}: 0 个高斯")

    # 分析锐度分布
    if sg_sharpness is not None:
        analyze_sharpness_distribution(sg_sharpness, verbose)

    if axis_counts:
        axis_counts = np.array(axis_counts)
        return axis_counts
    else:
        print("警告: 未找到有效的球面高斯基数据")
        return None


def count_gaussians_from_checkpoint(checkpoint_path, verbose=False):
    """
    从checkpoint文件中读取高斯数量和球面高斯基轴数统计
    支持v2版本的variable_sg_bands参数
    """
    print(f"正在读取checkpoint: {checkpoint_path}")

    try:
        # 加载checkpoint
        checkpoint_data = torch.load(checkpoint_path, map_location=torch.device('cpu'))

        # 检查checkpoint格式
        if isinstance(checkpoint_data, tuple) and len(checkpoint_data) == 2:
            # 标准格式: (model_params, iteration)
            model_params, iteration = checkpoint_data
            print(f"Checkpoint迭代次数: {iteration}")
        else:
            # 只有模型参数
            model_params = checkpoint_data
            print("无法确定Checkpoint迭代次数")

        # 检查model_params的格式
        if isinstance(model_params, dict):
            # 从SphericalGaussianModelcullSGv2.capture()返回的字典格式
            if "xyz" in model_params:
                xyz = model_params["xyz"]
                num_gaussians = xyz.shape[0]
                print(f"高斯数量: {num_gaussians}")

                # 打印球面高斯基信息
                if "active_sg_degree" in model_params:
                    print(f"活跃球面高斯基度数: {model_params['active_sg_degree']}")

                # 检查是否支持variable_sg_bands
                variable_sg_bands = model_params.get("variable_sg_bands", False)
                print(f"Variable SG Bands: {variable_sg_bands}")

                # 分析球面高斯基数据
                if "sg_directions" in model_params:
                    sg_directions = model_params["sg_directions"]
                    sg_sharpness = model_params.get("sg_sharpness")
                    sg_rgb = model_params.get("sg_rgb")

                    if variable_sg_bands and isinstance(sg_directions, list):
                        # 列表格式 (variable_sg_bands=True)
                        print("\n=== 列表格式数据 ===")
                        if verbose:
                            for i, tensor in enumerate(sg_directions):
                                if tensor.numel() > 0:
                                    print(f"度数 {i} 方向张量形状: {tensor.shape}")
                                else:
                                    print(f"度数 {i}: 空张量")

                        # 分析variable_sg_bands数据
                        axis_counts = analyze_variable_sg_bands_data(sg_directions, sg_sharpness, sg_rgb, verbose)
                        if axis_counts is not None:
                            analyze_axis_statistics(axis_counts, verbose)
                    else:
                        # 统一格式 (variable_sg_bands=False)
                        print("\n=== 统一格式数据 ===")
                        if verbose and hasattr(sg_directions, 'shape'):
                            print(f"球面高斯基方向张量形状: {sg_directions.shape}")
                            if sg_sharpness is not None and hasattr(sg_sharpness, 'shape'):
                                print(f"球面高斯基锐度张量形状: {sg_sharpness.shape}")
                            if sg_rgb is not None and hasattr(sg_rgb, 'shape'):
                                print(f"球面高斯基RGB张量形状: {sg_rgb.shape}")

                        # 分析锐度分布
                        if sg_sharpness is not None:
                            analyze_sharpness_distribution(sg_sharpness, verbose)

                        # 分析轴数统计
                        if "sg_axis_count" in model_params:
                            axis_counts = model_params["sg_axis_count"]
                            if hasattr(axis_counts, 'cpu'):
                                axis_counts = axis_counts.cpu().numpy()
                            analyze_axis_statistics(axis_counts, verbose)
                        else:
                            print("警告: 未找到球面高斯基轴数信息")
                else:
                    print("注意: 未找到球面高斯基数据")

                return num_gaussians
            else:
                print("错误: 未找到xyz数据")

        elif isinstance(model_params, tuple):
            # 兼容旧格式的元组
            if len(model_params) >= 2:
                xyz = model_params[1]
                if hasattr(xyz, 'shape'):
                    num_gaussians = xyz.shape[0]
                    print(f"高斯数量: {num_gaussians}")
                    print("注意: 这是旧格式的checkpoint，无法获取球面高斯基轴数信息")
                    return num_gaussians
                else:
                    print("错误: xyz没有shape属性")
            else:
                print(f"错误: model_params元组长度不足 ({len(model_params)})")
        else:
            print(f"错误: model_params格式不支持，类型为 {type(model_params)}")

    except Exception as e:
        print(f"读取checkpoint时出错: {e}")
        import traceback
        traceback.print_exc()

    return None


def count_gaussians_from_ply(ply_path, verbose=False):
    """
    从PLY文件中读取高斯数量和球面高斯基轴数统计
    支持v2版本的variable_sg_bands格式（多个vertex_X组）
    """
    print(f"正在读取PLY文件: {ply_path}")

    try:
        # 读取PLY文件
        plydata = PlyData.read(ply_path)

        # 计算高斯数量
        total_gaussians = 0
        axis_counts_all = []

        # 检查是否有多个顶点组（对应variable_sg_bands=True的不同度数）
        vertex_groups = []
        variable_sg_bands_format = False

        for element in plydata.elements:
            if element.name.startswith('vertex'):
                vertex_groups.append(element)
                if element.name.startswith('vertex_') and element.name != 'vertex':
                    variable_sg_bands_format = True

        if not vertex_groups:
            print("警告: 未找到顶点组")
            return None

        print(f"检测到格式: {'Variable SG Bands (列表格式)' if variable_sg_bands_format else '统一格式'}")

        if variable_sg_bands_format:
            # Variable SG Bands格式：每个度数一个vertex_X组
            print(f"\n=== Variable SG Bands 格式分析 ===")

            degree_groups = {}
            for element in vertex_groups:
                if element.name == 'vertex':
                    # 可能是度数0的组
                    degree = 0
                elif element.name.startswith('vertex_'):
                    try:
                        degree = int(element.name.split('_')[1])
                    except (IndexError, ValueError):
                        print(f"警告: 无法解析顶点组名称 {element.name}")
                        continue
                else:
                    continue

                degree_groups[degree] = element
                num_gaussians = len(element.data)
                total_gaussians += num_gaussians

                print(f"度数 {degree}: {num_gaussians} 个高斯")

                # 为这个度数的所有高斯添加轴数记录
                axis_counts_all.extend([degree] * num_gaussians)

                if verbose and 'sg_axis_count' in element.data.dtype.names:
                    axis_counts_element = np.asarray(element.data['sg_axis_count'])
                    print(f"  度数 {degree} 轴数记录: min={np.min(axis_counts_element)}, max={np.max(axis_counts_element)}")

            # 分析锐度分布
            analyze_sharpness_distribution_from_ply(vertex_groups, verbose)

            if axis_counts_all:
                axis_counts_all = np.array(axis_counts_all)
                analyze_axis_statistics(axis_counts_all, verbose)
        else:
            # 统一格式：单个vertex组
            print(f"\n=== 统一格式分析 ===")
            main_element = vertex_groups[0]
            total_gaussians = len(main_element.data)
            print(f"总高斯数量: {total_gaussians}")

            if verbose:
                print(f"\nPLY文件属性列表:")
                for name in main_element.data.dtype.names:
                    if name.startswith('sg_'):
                        print(f"  {name}")

            # 分析锐度分布
            analyze_sharpness_distribution_from_ply([main_element], verbose)

            # 检查是否有轴数信息
            if 'sg_axis_count' in main_element.data.dtype.names:
                axis_counts = np.asarray(main_element.data['sg_axis_count'])
                analyze_axis_statistics(axis_counts, verbose)

                # 额外分析：检查球面高斯基数据的一致性
                if verbose:
                    print(f"\n=== 数据一致性检查 ===")
                    # 检查是否有球面高斯基相关的其他属性
                    sg_attributes = [name for name in main_element.data.dtype.names if name.startswith('sg_')]
                    print(f"找到 {len(sg_attributes)} 个球面高斯基相关属性")

                    # 尝试推断最大轴数
                    max_axis_from_attributes = 0
                    for attr in sg_attributes:
                        if '_' in attr and len(attr.split('_')) > 1 and attr.split('_')[1].isdigit():
                            axis_idx = int(attr.split('_')[1])
                            max_axis_from_attributes = max(max_axis_from_attributes, axis_idx + 1)

                    if max_axis_from_attributes > 0:
                        print(f"从属性名推断的最大轴数: {max_axis_from_attributes}")
                        actual_max_axis = np.max(axis_counts)
                        print(f"实际记录的最大轴数: {actual_max_axis}")

                        if max_axis_from_attributes != actual_max_axis:
                            print("警告: 属性数量与记录的轴数不一致！")
            else:
                print("注意: PLY文件中未找到球面高斯基轴数信息")

        print(f"\n总高斯数量: {total_gaussians}")
        return total_gaussians

    except Exception as e:
        print(f"读取PLY文件时出错: {e}")
        import traceback
        traceback.print_exc()

    return None


def main():
    parser = argparse.ArgumentParser(description="读取spherical_gaussian_model_cullSG_v2.py中输出的checkpoint或.ply文件，获取对应的高斯数量和球面高斯基轴数统计，支持variable_sg_bands参数")
    parser.add_argument("file_path", help="checkpoint或.ply文件的路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细信息")
    args = parser.parse_args()

    if not os.path.exists(args.file_path):
        print(f"错误: 文件不存在 - {args.file_path}")
        return 1

    print("=" * 70)
    print("球面高斯模型统计分析工具 (v2 - 支持Variable SG Bands)")
    print("=" * 70)

    # 根据文件扩展名决定使用哪个函数
    if args.file_path.lower().endswith('.pth'):
        result = count_gaussians_from_checkpoint(args.file_path, args.verbose)
    elif args.file_path.lower().endswith('.ply'):
        result = count_gaussians_from_ply(args.file_path, args.verbose)
    else:
        print(f"错误: 不支持的文件类型 - {args.file_path}")
        print("支持的文件类型: .pth (checkpoint), .ply")
        return 1

    if result is not None:
        print("\n" + "=" * 60)
        print(f"分析完成！总高斯数量: {result}")
        print("=" * 60)
    else:
        print("\n分析失败！")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
